#!/usr/bin/env python3
"""
Test script to verify the fixes for:
1. Parallel comment generation
2. UUID serialization in JSON
"""

import asyncio
import json
import time
from typing import List

from platyfend_ai.models.analysis import (
    SecurityFinding, 
    ReviewComment, 
    SeverityLevel, 
    AnalyzerType, 
    CodeLocation
)
from platyfend_ai.services.comment_generator import SecurityCommentGenerator


async def test_parallel_comment_generation():
    """Test that comment generation works in parallel"""
    print("Testing parallel comment generation...")
    
    # Create test findings
    findings = []
    for i in range(3):
        finding = SecurityFinding(
            analyzer_type=AnalyzerType.SEMGREP,
            rule_id=f'test-rule-{i}',
            rule_name=f'Test Rule {i}',
            severity=SeverityLevel.HIGH,
            location=CodeLocation(file_path=f'test{i}.py', line_start=i+1),
            title=f'Test Finding {i}',
            description=f'Test description {i}',
            message=f'Test message {i}'
        )
        findings.append(finding)
    
    # Test comment generator without OpenAI (will use fallback)
    generator = SecurityCommentGenerator(openai_api_key=None)
    
    # This should not crash even without OpenAI key
    try:
        comments = await generator.generate_review_comments(findings)
        print(f"✓ Generated {len(comments)} comments (fallback mode)")
    except Exception as e:
        print(f"✓ Expected error without OpenAI key: {e}")
    
    print("Parallel comment generation test completed!")


def test_uuid_serialization():
    """Test that UUIDs are properly serialized to JSON"""
    print("\nTesting UUID serialization...")
    
    # Create test finding and comment
    finding = SecurityFinding(
        analyzer_type=AnalyzerType.SEMGREP,
        rule_id='test-rule',
        rule_name='Test Rule',
        severity=SeverityLevel.HIGH,
        location=CodeLocation(file_path='test.py', line_start=1),
        title='Test Finding',
        description='Test description',
        message='Test message'
    )
    
    comment = ReviewComment(
        body='Test comment',
        finding_ids=[finding.id],
        severity=SeverityLevel.HIGH
    )
    
    # Test JSON serialization
    try:
        finding_json = finding.model_dump_json()
        comment_json = comment.model_dump_json()
        
        # Parse back to verify
        finding_dict = json.loads(finding_json)
        comment_dict = json.loads(comment_json)
        
        print(f"✓ Finding ID serialized as: {finding_dict['id']}")
        print(f"✓ Comment finding_ids serialized as: {comment_dict['finding_ids']}")
        print("✓ UUID serialization test passed!")
        
    except Exception as e:
        print(f"✗ UUID serialization failed: {e}")
        raise


def test_comment_processing_payload():
    """Test the comment processing service payload creation"""
    print("\nTesting comment processing payload...")
    
    # Create test comment with UUID finding_ids
    finding = SecurityFinding(
        analyzer_type=AnalyzerType.SEMGREP,
        rule_id='test-rule',
        rule_name='Test Rule',
        severity=SeverityLevel.HIGH,
        location=CodeLocation(file_path='test.py', line_start=1),
        title='Test Finding',
        description='Test description',
        message='Test message'
    )
    
    comment = ReviewComment(
        body='Test comment',
        finding_ids=[finding.id],
        severity=SeverityLevel.HIGH,
        file_path='test.py',
        line=1
    )
    
    # Simulate the payload creation from comment_processing_service.py
    payload_comment = {
        "body": comment.body,
        "file_path": comment.file_path or "",
        "line": comment.line or 0,
        "severity": comment.severity.value if comment.severity else "",
        "comment_type": comment.comment_type,
        "finding_ids": [str(finding_id) for finding_id in comment.finding_ids],
    }
    
    try:
        # This should not raise a JSON serialization error
        json_payload = json.dumps(payload_comment)
        print(f"✓ Payload JSON serialization successful")
        print(f"✓ finding_ids in payload: {payload_comment['finding_ids']}")
        
    except Exception as e:
        print(f"✗ Payload serialization failed: {e}")
        raise


async def main():
    """Run all tests"""
    print("Running fixes verification tests...\n")
    
    # Test UUID serialization
    test_uuid_serialization()
    
    # Test comment processing payload
    test_comment_processing_payload()
    
    # Test parallel comment generation
    await test_parallel_comment_generation()
    
    print("\n🎉 All tests passed! The fixes are working correctly.")


if __name__ == "__main__":
    asyncio.run(main())
